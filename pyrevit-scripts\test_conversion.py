#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Script de teste para conversão RFA → OBJ
Versão simplificada para debug
"""

import sys
import os

print("=== TESTE BIMEX CONVERTER ===")
print("Python version: " + str(sys.version))
print("Arguments: " + str(sys.argv))

try:
    import clr
    print("CLR importado com sucesso")
    
    # Adicionar referências do Revit
    clr.AddReference('RevitAPI')
    clr.AddReference('RevitAPIUI')
    print("Referencias Revit adicionadas")
    
    from Autodesk.Revit.DB import *
    print("Autodesk.Revit.DB importado")
    
    from pyrevit import revit
    print("pyrevit importado")
    
    # Testar acesso ao Revit
    app = revit.app
    if app is None:
        print("ERRO: app e None")
    else:
        print("App obtido: " + str(type(app)))
        print("Versao: " + app.VersionName)
    
    # Obter argumentos
    if len(sys.argv) < 2:
        print("ERRO: Arquivo RFA nao especificado")
        sys.exit(1)
    
    input_file = sys.argv[1]
    print("Arquivo de entrada: " + input_file)
    
    if not os.path.exists(input_file):
        print("ERRO: Arquivo nao existe")
        sys.exit(1)
    
    # Tentar abrir documento
    print("Tentando abrir documento...")
    doc = app.OpenDocumentFile(input_file)
    
    if doc is None:
        print("ERRO: Falha ao abrir documento")
        sys.exit(1)
    
    print("Documento aberto: " + doc.Title)
    print("E familia: " + str(doc.IsFamilyDocument))
    
    # Coletar elementos
    collector = FilteredElementCollector(doc)
    elements = list(collector.WhereElementIsNotElementType().ToElements())
    print("Elementos encontrados: " + str(len(elements)))
    
    # Contar elementos com geometria
    geom_count = 0
    for element in elements:
        try:
            geom = element.get_Geometry(Options())
            if geom is not None:
                for geom_obj in geom:
                    if isinstance(geom_obj, Solid) and geom_obj.Volume > 0:
                        geom_count += 1
                        break
                    elif isinstance(geom_obj, GeometryInstance):
                        inst_geom = geom_obj.GetInstanceGeometry()
                        for inst_obj in inst_geom:
                            if isinstance(inst_obj, Solid) and inst_obj.Volume > 0:
                                geom_count += 1
                                break
                        if geom_count > 0:
                            break
        except:
            continue
    
    print("Elementos com geometria: " + str(geom_count))
    
    # Criar arquivo OBJ simples
    output_file = os.path.splitext(input_file)[0] + "_test.obj"
    print("Criando arquivo: " + output_file)
    
    with open(output_file, 'w') as f:
        f.write("# BIMEX Test Converter\n")
        f.write("# Familia: " + doc.Title + "\n")
        f.write("# Elementos: " + str(len(elements)) + "\n")
        f.write("# Elementos com geometria: " + str(geom_count) + "\n")
        f.write("# Teste realizado com sucesso\n\n")
        
        # Cubo simples para teste
        f.write("v -1.0 -1.0 -1.0\n")
        f.write("v  1.0 -1.0 -1.0\n")
        f.write("v  1.0  1.0 -1.0\n")
        f.write("v -1.0  1.0 -1.0\n")
        f.write("v -1.0 -1.0  1.0\n")
        f.write("v  1.0 -1.0  1.0\n")
        f.write("v  1.0  1.0  1.0\n")
        f.write("v -1.0  1.0  1.0\n\n")
        
        f.write("f 1 2 3 4\n")
        f.write("f 5 8 7 6\n")
        f.write("f 1 5 6 2\n")
        f.write("f 2 6 7 3\n")
        f.write("f 3 7 8 4\n")
        f.write("f 5 1 4 8\n")
    
    print("Arquivo criado com sucesso!")
    
    # Fechar documento
    doc.Close(False)
    print("Documento fechado")
    
    print("=== TESTE CONCLUIDO COM SUCESSO ===")
    
except Exception as e:
    print("ERRO: " + str(e))
    import traceback
    traceback.print_exc()
    sys.exit(1)
