#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Script de teste para o conversor BIMEX pyRevit
Testa a funcionalidade de conversão RFA → OBJ

Uso:
    python test_converter.py [--test-file arquivo.rfa]
"""

import os
import sys
import tempfile
import argparse
import logging
from pathlib import Path

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_environment():
    """Testa se o ambiente está configurado corretamente"""
    logger.info("🔍 Testando ambiente...")

    tests = []

    # Teste 1: Python disponível
    try:
        import sys
        python_version = sys.version
        tests.append(("Python", True, f"Versão: {python_version}"))
    except Exception as e:
        tests.append(("Python", False, str(e)))

    # Teste 2: Scripts existem
    script_dir = os.path.dirname(os.path.abspath(__file__))
    export_script = os.path.join(script_dir, 'export_obj.py')
    converter_script = os.path.join(script_dir, 'bimex_converter.py')

    tests.append(("export_obj.py", os.path.exists(export_script), export_script))
    tests.append(("bimex_converter.py", os.path.exists(converter_script), converter_script))

    # Teste 3: Diretório temporário
    try:
        temp_dir = tempfile.gettempdir()
        test_file = os.path.join(temp_dir, 'bimex_test.tmp')
        with open(test_file, 'w') as f:
            f.write('test')
        os.remove(test_file)
        tests.append(("Diretório temporário", True, temp_dir))
    except Exception as e:
        tests.append(("Diretório temporário", False, str(e)))

    # Teste 4: APIs do Revit (opcional)
    try:
        import clr
        clr.AddReference('RevitAPI')
        import Autodesk.Revit.DB as RevitDB
        tests.append(("APIs do Revit", True, "Disponíveis"))
    except Exception as e:
        tests.append(("APIs do Revit", False, f"Não disponíveis: {e}"))

    # Exibir resultados
    logger.info("📋 Resultados dos testes:")
    all_passed = True
    revit_available = False

    for test_name, passed, details in tests:
        status = "✅" if passed else "❌"
        logger.info(f"  {status} {test_name}: {details}")

        # APIs do Revit são opcionais - não falha o teste se não estiverem disponíveis
        if test_name == "APIs do Revit":
            revit_available = passed
            if not passed:
                logger.info("  💡 APIs do Revit não disponíveis - usando modo alternativo")
        elif not passed:
            all_passed = False

    if not revit_available:
        logger.info("⚠️ Modo alternativo será usado (sem pyRevit)")
        logger.info("📝 Para conversão real, instale pyRevit e Revit")

    return all_passed

def create_test_rfa():
    """Cria um arquivo RFA de teste simples (mock)"""
    logger.info("📝 Criando arquivo RFA de teste...")

    # Criar arquivo mock que simula estrutura RFA
    # Nota: Este não é um RFA real, apenas para testar o pipeline
    test_content = b'\xD0\xCF\x11\xE0\xA1\xB1\x1A\xE1' + b'\x00' * 1000  # Simula header OLE

    temp_dir = tempfile.gettempdir()
    test_rfa = os.path.join(temp_dir, 'bimex_test_family.rfa')

    try:
        with open(test_rfa, 'wb') as f:
            f.write(test_content)

        logger.info(f"✅ Arquivo de teste criado: {test_rfa}")
        return test_rfa

    except Exception as e:
        logger.error(f"❌ Erro ao criar arquivo de teste: {e}")
        return None

def test_validation(test_file):
    """Testa validação de arquivos"""
    logger.info("🔍 Testando validação de arquivos...")

    try:
        from bimex_converter import BimexConverter

        # Teste com arquivo válido
        is_valid, message = BimexConverter.validate_rfa_file(test_file)
        logger.info(f"  Validação: {is_valid} - {message}")

        # Teste com arquivo inexistente
        is_valid, message = BimexConverter.validate_rfa_file("arquivo_inexistente.rfa")
        logger.info(f"  Arquivo inexistente: {is_valid} - {message}")

        # Teste informações do arquivo
        info = BimexConverter.get_family_info(test_file)
        if info:
            logger.info(f"  Informações: {info}")

        return True

    except Exception as e:
        logger.error(f"❌ Erro no teste de validação: {e}")
        return False

def test_conversion_pipeline(test_file):
    """Testa o pipeline de conversão completo"""
    logger.info("🔄 Testando pipeline de conversão...")

    try:
        from bimex_converter import convert_rfa_to_obj

        # Preparar arquivo de saída
        temp_dir = tempfile.gettempdir()
        output_file = os.path.join(temp_dir, 'bimex_test_output.obj')

        # Executar conversão
        result = convert_rfa_to_obj(test_file, output_file)

        logger.info("📊 Resultado da conversão:")
        logger.info(f"  Sucesso: {result.get('success', False)}")
        logger.info(f"  Arquivo entrada: {result.get('input_path', 'N/A')}")
        logger.info(f"  Arquivo saída: {result.get('output_path', 'N/A')}")

        if not result.get('success'):
            logger.info(f"  Erro: {result.get('error', 'N/A')}")
            logger.info(f"  STDOUT: {result.get('stdout', 'N/A')}")
            logger.info(f"  STDERR: {result.get('stderr', 'N/A')}")

        # Limpar arquivo de saída se foi criado
        if os.path.exists(output_file):
            os.remove(output_file)
            logger.info("🧹 Arquivo de teste removido")

        return result.get('success', False)

    except Exception as e:
        logger.error(f"❌ Erro no teste de conversão: {e}")
        return False

def test_typescript_integration():
    """Testa integração com TypeScript (simulação)"""
    logger.info("🔗 Testando integração TypeScript...")

    try:
        # Simular chamada do TypeScript
        import subprocess
        import json

        script_dir = os.path.dirname(os.path.abspath(__file__))
        converter_script = os.path.join(script_dir, 'bimex_converter.py')

        # Teste de informações
        cmd = [sys.executable, converter_script, '--input', 'arquivo_teste.rfa', '--info']

        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=10
        )

        logger.info(f"  Código de retorno: {result.returncode}")
        logger.info(f"  STDOUT: {result.stdout[:200]}...")

        if result.stderr:
            logger.info(f"  STDERR: {result.stderr[:200]}...")

        return result.returncode in [0, 1]  # 0 = sucesso, 1 = erro esperado (arquivo não existe)

    except Exception as e:
        logger.error(f"❌ Erro no teste de integração: {e}")
        return False

def cleanup_test_files():
    """Remove arquivos de teste"""
    logger.info("🧹 Limpando arquivos de teste...")

    temp_dir = tempfile.gettempdir()
    test_files = [
        'bimex_test_family.rfa',
        'bimex_test_output.obj',
        'bimex_test.tmp'
    ]

    for filename in test_files:
        filepath = os.path.join(temp_dir, filename)
        try:
            if os.path.exists(filepath):
                os.remove(filepath)
                logger.info(f"  ✅ Removido: {filename}")
        except Exception as e:
            logger.warning(f"  ⚠️ Erro ao remover {filename}: {e}")

def main():
    """Função principal de teste"""
    parser = argparse.ArgumentParser(description="Teste do conversor BIMEX pyRevit")
    parser.add_argument(
        '--test-file',
        help="Arquivo RFA específico para testar (opcional)"
    )
    parser.add_argument(
        '--skip-conversion',
        action='store_true',
        help="Pular teste de conversão (apenas ambiente)"
    )

    args = parser.parse_args()

    logger.info("🚀 Iniciando testes do conversor BIMEX...")

    # Teste 1: Ambiente
    logger.info("\n" + "="*50)
    logger.info("TESTE 1: AMBIENTE")
    logger.info("="*50)

    env_ok = test_environment()

    if not env_ok:
        logger.error("❌ Ambiente não está configurado corretamente")
        logger.info("💡 Verifique a instalação do pyRevit e Revit")
        return 1

    # Preparar arquivo de teste
    test_file = args.test_file
    if not test_file:
        test_file = create_test_rfa()
        if not test_file:
            logger.error("❌ Não foi possível criar arquivo de teste")
            return 1

    # Teste 2: Validação
    logger.info("\n" + "="*50)
    logger.info("TESTE 2: VALIDAÇÃO")
    logger.info("="*50)

    validation_ok = test_validation(test_file)

    # Teste 3: Conversão (opcional)
    if not args.skip_conversion:
        logger.info("\n" + "="*50)
        logger.info("TESTE 3: CONVERSÃO")
        logger.info("="*50)

        conversion_ok = test_conversion_pipeline(test_file)
    else:
        logger.info("\n⏭️ Teste de conversão pulado")
        conversion_ok = True

    # Teste 4: Integração TypeScript
    logger.info("\n" + "="*50)
    logger.info("TESTE 4: INTEGRAÇÃO")
    logger.info("="*50)

    integration_ok = test_typescript_integration()

    # Limpeza
    if not args.test_file:  # Só limpar se criamos o arquivo
        cleanup_test_files()

    # Resultado final
    logger.info("\n" + "="*50)
    logger.info("RESULTADO FINAL")
    logger.info("="*50)

    tests_results = [
        ("Ambiente", env_ok),
        ("Validação", validation_ok),
        ("Conversão", conversion_ok),
        ("Integração", integration_ok)
    ]

    all_passed = True
    for test_name, passed in tests_results:
        status = "✅" if passed else "❌"
        logger.info(f"  {status} {test_name}")
        if not passed:
            all_passed = False

    if all_passed:
        logger.info("\n🎉 Todos os testes passaram!")
        logger.info("💡 Sistema pronto para conversão RFA → OBJ")
        return 0
    else:
        logger.error("\n❌ Alguns testes falharam")
        logger.info("💡 Verifique a configuração antes de usar em produção")
        return 1

if __name__ == "__main__":
    sys.exit(main())
