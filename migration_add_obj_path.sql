-- Migração para adicionar campo obj_path à tabela families
-- Adiciona suporte para armazenar o caminho do arquivo OBJ convertido

-- Adicionar coluna obj_path para armazenar o caminho do arquivo OBJ convertido
ALTER TABLE families 
ADD COLUMN IF NOT EXISTS obj_path VARCHAR(500);

-- Adicionar comentário para documentar o campo
COMMENT ON COLUMN families.obj_path IS 'Caminho do arquivo OBJ convertido automaticamente pelo pyRevit (apenas para arquivos RFA)';

-- Criar índice para melhorar performance de consultas que filtram por arquivos com OBJ
CREATE INDEX IF NOT EXISTS idx_families_obj_path ON families(obj_path) WHERE obj_path IS NOT NULL;

-- Atualizar a consulta de exemplo para incluir o novo campo
-- SELECT id, title, file_path, image_path, obj_path, description, room, category, tags, manufacturer, minimal_revit_version, price, premium, product_link, key_features, detailed_description, detailed_specification, created_at FROM families ORDER BY created_at DESC;

-- Verificar se a migração foi aplicada corretamente
SELECT 
    column_name, 
    data_type, 
    is_nullable, 
    column_default
FROM information_schema.columns 
WHERE table_name = 'families' 
    AND column_name = 'obj_path';
