#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Conversor standalone RFA → OBJ usando Python e APIs do Revit
Funciona independentemente do pyRevit
"""

import sys
import os
import subprocess
import tempfile

def create_pyrevit_script(input_file, output_file):
    """Cria script pyRevit temporário para conversão"""
    
    script_content = '''
import sys
import os
import clr

# Adicionar referências do Revit
clr.AddReference('RevitAPI')
clr.AddReference('RevitAPIUI')

from Autodesk.Revit.DB import *
from pyrevit import revit

def main():
    input_file = r"{input_file}"
    output_file = r"{output_file}"
    
    print("=== BIMEX RFA to OBJ Converter ===")
    print("Input: " + input_file)
    print("Output: " + output_file)
    
    try:
        # Obter aplicação
        app = revit.app
        print("App obtido")
        
        # Abrir documento
        doc = app.OpenDocumentFile(input_file)
        if doc is None:
            print("ERRO: Falha ao abrir documento")
            return False
        
        print("Documento aberto: " + doc.Title)
        
        # Verificar se é família
        if not doc.IsFamilyDocument:
            print("ERRO: Nao e familia")
            doc.Close(False)
            return False
        
        # Coletar elementos
        collector = FilteredElementCollector(doc)
        elements = list(collector.WhereElementIsNotElementType().ToElements())
        print("Elementos: " + str(len(elements)))
        
        # Processar geometria
        vertices = []
        faces = []
        vertex_index = 1
        
        for element in elements:
            try:
                geom = element.get_Geometry(Options())
                if geom is None:
                    continue
                
                for geom_obj in geom:
                    if isinstance(geom_obj, Solid) and geom_obj.Volume > 0:
                        for face in geom_obj.Faces:
                            try:
                                mesh = face.Triangulate()
                                if mesh is None:
                                    continue
                                
                                for i in range(mesh.NumTriangles):
                                    triangle = mesh.get_Triangle(i)
                                    
                                    v_indices = []
                                    for j in range(3):
                                        vertex = triangle.get_Vertex(j)
                                        x = vertex.X * 0.3048
                                        y = vertex.Y * 0.3048
                                        z = vertex.Z * 0.3048
                                        
                                        vertices.append("v " + str(x) + " " + str(y) + " " + str(z))
                                        v_indices.append(vertex_index)
                                        vertex_index += 1
                                    
                                    faces.append("f " + str(v_indices[0]) + " " + str(v_indices[1]) + " " + str(v_indices[2]))
                            except:
                                continue
                                
                    elif isinstance(geom_obj, GeometryInstance):
                        try:
                            inst_geom = geom_obj.GetInstanceGeometry()
                            for inst_obj in inst_geom:
                                if isinstance(inst_obj, Solid) and inst_obj.Volume > 0:
                                    for face in inst_obj.Faces:
                                        try:
                                            mesh = face.Triangulate()
                                            if mesh is None:
                                                continue
                                            
                                            for i in range(mesh.NumTriangles):
                                                triangle = mesh.get_Triangle(i)
                                                
                                                v_indices = []
                                                for j in range(3):
                                                    vertex = triangle.get_Vertex(j)
                                                    x = vertex.X * 0.3048
                                                    y = vertex.Y * 0.3048
                                                    z = vertex.Z * 0.3048
                                                    
                                                    vertices.append("v " + str(x) + " " + str(y) + " " + str(z))
                                                    v_indices.append(vertex_index)
                                                    vertex_index += 1
                                                
                                                faces.append("f " + str(v_indices[0]) + " " + str(v_indices[1]) + " " + str(v_indices[2]))
                                        except:
                                            continue
                        except:
                            continue
            except:
                continue
        
        print("Vertices: " + str(len(vertices)))
        print("Faces: " + str(len(faces)))
        
        # Criar arquivo OBJ
        with open(output_file, 'w') as f:
            f.write("# BIMEX RFA to OBJ Converter\\n")
            f.write("# Familia: " + doc.Title + "\\n")
            f.write("# Vertices: " + str(len(vertices)) + "\\n")
            f.write("# Faces: " + str(len(faces)) + "\\n\\n")
            
            for vertex in vertices:
                f.write(vertex + "\\n")
            
            f.write("\\n")
            
            for face in faces:
                f.write(face + "\\n")
        
        print("Arquivo criado: " + output_file)
        
        # Fechar documento
        doc.Close(False)
        print("Documento fechado")
        
        return True
        
    except Exception as e:
        print("ERRO: " + str(e))
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("=== CONVERSAO CONCLUIDA ===")
    else:
        print("=== CONVERSAO FALHOU ===")
'''.format(input_file=input_file, output_file=output_file)
    
    return script_content

def convert_rfa_to_obj(input_file, output_file=None):
    """Converte arquivo RFA para OBJ"""
    
    print("=== BIMEX Standalone Converter ===")
    print(f"Arquivo RFA: {input_file}")
    
    # Verificar se arquivo existe
    if not os.path.exists(input_file):
        print(f"ERRO: Arquivo não encontrado: {input_file}")
        return False
    
    # Gerar nome do arquivo OBJ
    if output_file is None:
        output_file = os.path.splitext(input_file)[0] + ".obj"
    
    print(f"Arquivo OBJ: {output_file}")
    
    # Criar script temporário
    with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as temp_script:
        script_content = create_pyrevit_script(input_file, output_file)
        temp_script.write(script_content)
        temp_script_path = temp_script.name
    
    try:
        # Executar com pyRevit
        pyrevit_exe = r"C:\Users\<USER>\AppData\Roaming\pyRevit-Master\bin\pyrevit.exe"
        
        cmd = [
            pyrevit_exe,
            'run',
            temp_script_path,
            '--revit=2024'
        ]
        
        print("Executando comando:")
        print(" ".join(cmd))
        
        # Executar processo
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=300  # 5 minutos
        )
        
        print("Return code:", result.returncode)
        print("STDOUT:", result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)
        
        # Verificar se arquivo foi criado
        if os.path.exists(output_file):
            file_size = os.path.getsize(output_file)
            print(f"✅ Arquivo OBJ criado com sucesso!")
            print(f"📁 Localização: {output_file}")
            print(f"📏 Tamanho: {file_size} bytes")
            return True
        else:
            print("❌ Arquivo OBJ não foi criado")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Timeout na execução")
        return False
    except Exception as e:
        print(f"❌ Erro na execução: {e}")
        return False
    finally:
        # Limpar arquivo temporário
        try:
            os.unlink(temp_script_path)
        except:
            pass

def main():
    """Função principal"""
    if len(sys.argv) < 2:
        print("Uso: python standalone_converter.py arquivo.rfa [arquivo.obj]")
        return 1
    
    input_file = sys.argv[1]
    output_file = sys.argv[2] if len(sys.argv) > 2 else None
    
    success = convert_rfa_to_obj(input_file, output_file)
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
