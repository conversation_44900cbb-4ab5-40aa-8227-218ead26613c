# BIMEX Object Market

Marketplace premium de famílias BIM para Revit com conversão automática para formato OBJ usando pyRevit.

## 🚀 Características

- ✅ **Upload de famílias Revit** (.rfa) com preview automático
- ✅ **Conversão automática RFA → OBJ** usando pyRevit (sem ODA BIMRV)
- ✅ **IA integrada** para geração automática de títulos e descrições
- ✅ **Interface premium** com shadcn/ui e Tailwind CSS
- ✅ **Armazenamento na nuvem** com Cloudflare R2
- ✅ **Plugin Revit** para download direto no software
- ✅ **Sistema de categorização** avançado
- ✅ **Busca e filtros** inteligentes

## 🛠️ Tecnologias

### Frontend
- **Next.js 15** (App Router)
- **React 18** com TypeScript
- **Tailwind CSS** + **shadcn/ui**
- **Lucide Icons**

### Backend
- **PostgreSQL** (banco de dados)
- **Cloudflare R2** (armazenamento de arquivos)
- **Google Gemini API** (IA para metadados)

### Conversão de Arquivos
- **pyRevit** (conversão RFA → OBJ)
- **Revit API** (processamento de famílias)
- **Python 3.7+** (scripts de conversão)

### Plugin Revit
- **C# .NET Framework 4.8**
- **Revit API 2024**
- **WebView2** (interface integrada)

## 📋 Pré-requisitos

### Software Necessário

1. **Node.js** 18+ e npm
2. **PostgreSQL** 12+
3. **Revit** 2020+ (para conversão)
4. **pyRevit** ([instalação](https://github.com/eirannejad/pyRevit))
5. **Python** 3.7+

### Contas e APIs

1. **Cloudflare R2** (armazenamento)
2. **Google AI Studio** (API Gemini)

## 🚀 Instalação

### 1. Clone o repositório

```bash
git clone https://github.com/seu-usuario/bimex-object-market.git
cd bimex-object-market
```

### 2. Instale dependências

```bash
npm install
```

### 3. Configure variáveis de ambiente

```bash
cp .env.example .env
```

Edite o arquivo `.env` com suas configurações:

```env
# Banco de dados
DATABASE_URL=postgresql://usuario:senha@localhost:5432/bimex_db

# Cloudflare R2
CLOUDFLARE_R2_ENDPOINT=https://sua-conta.r2.cloudflarestorage.com
CLOUDFLARE_R2_ACCESS_KEY_ID=sua_access_key
CLOUDFLARE_R2_SECRET_ACCESS_KEY=sua_secret_key
CLOUDFLARE_R2_BUCKET=bimex-families

# Google Gemini
GOOGLE_GEMINI_API_KEY=sua_api_key

# pyRevit Converter
PYTHON_PATH=python
ENABLE_OBJ_CONVERSION=true
```

### 4. Configure o banco de dados

```bash
# Criar banco
createdb bimex_db

# Executar migrações
psql -d bimex_db -f families.sql
psql -d bimex_db -f migration_add_obj_path.sql
```

### 5. Teste o conversor pyRevit

```bash
python pyrevit-scripts/test_converter.py
```

### 6. Execute o projeto

```bash
npm run dev
```

Acesse [http://localhost:3000](http://localhost:3000)

## 🔄 Sistema de Conversão RFA → OBJ

### Como Funciona

1. **Upload**: Usuário faz upload de arquivo .rfa
2. **Detecção**: Sistema detecta automaticamente arquivos Revit
3. **Conversão**: pyRevit converte RFA para OBJ em background
4. **Armazenamento**: Ambos os arquivos são salvos no Cloudflare R2
5. **Disponibilização**: URLs de download ficam disponíveis na API

### Arquitetura

```
Upload RFA → Validação → pyRevit Converter → OBJ → S3 Upload → Database Update
     ↓              ↓            ↓           ↓         ↓            ↓
  Interface     Validation   Python Script  3D Model  Cloud Store  API Response
```

### Scripts pyRevit

- **`export_obj.py`**: Conversão principal usando Revit API
- **`bimex_converter.py`**: Interface de alto nível
- **`test_converter.py`**: Testes e validação

## 🔌 Plugin Revit

### Instalação

1. Compile o projeto C#:
```bash
cd plugin-revit
dotnet build -c Release
```

2. Copie arquivos para pasta de plugins do Revit:
```bash
# Exemplo para Revit 2024
copy bin\Release\*.dll "%APPDATA%\Autodesk\Revit\Addins\2024\"
copy BimexPlugin.addin "%APPDATA%\Autodesk\Revit\Addins\2024\"
```

### Uso

1. Abra o Revit
2. Vá para aba "Complementos"
3. Clique no botão "BIMEX"
4. Navegue pelo catálogo integrado
5. Clique em "Baixar" para carregar famílias diretamente

## 📁 Estrutura do Projeto

```
bimex-object-market/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── api/families/       # API de famílias
│   │   ├── families-plugin/    # Interface para plugin
│   │   └── upload/             # Página de upload
│   ├── components/ui/          # Componentes shadcn/ui
│   └── lib/
│       └── bimrv-converter.ts  # Conversor TypeScript
├── pyrevit-scripts/            # Scripts de conversão
│   ├── export_obj.py          # Conversão principal
│   ├── bimex_converter.py     # Utilitários
│   ├── test_converter.py      # Testes
│   └── README.md              # Documentação
├── plugin-revit/              # Plugin C# para Revit
│   ├── OpenWebViewCommand.cs  # Comando principal
│   ├── BimexPlugin.addin      # Manifesto do plugin
│   └── RevitPluginWebView.csproj
├── families.sql               # Schema do banco
├── migration_add_obj_path.sql # Migração para OBJ
└── .env.example               # Configurações de exemplo
```

## 🧪 Testes

### Testar conversor pyRevit

```bash
# Teste completo
python pyrevit-scripts/test_converter.py

# Teste apenas ambiente (sem conversão)
python pyrevit-scripts/test_converter.py --skip-conversion

# Teste com arquivo específico
python pyrevit-scripts/test_converter.py --test-file caminho/para/familia.rfa
```

### Testar API

```bash
# Upload de família
curl -X POST http://localhost:3000/api/families \
  -F "title=Teste" \
  -F "file=@familia.rfa" \
  -F "image=@preview.jpg"

# Listar famílias
curl http://localhost:3000/api/families
```

## 🚀 Deploy

### Preparação

1. Configure variáveis de ambiente de produção
2. Execute migrações do banco
3. Teste conversão em ambiente de produção
4. Configure Cloudflare R2 com domínio personalizado

### Vercel (Recomendado)

```bash
# Instalar Vercel CLI
npm i -g vercel

# Deploy
vercel --prod
```

## 🔧 Troubleshooting

### Problemas Comuns

#### "APIs do Revit não estão disponíveis"
- Instale pyRevit e Revit
- Execute em ambiente com Revit disponível
- Verifique licença do Revit

#### "Conversão excedeu tempo limite"
- Aumente `CONVERTER_TIMEOUT` no .env
- Otimize arquivos RFA (remover elementos desnecessários)
- Verifique recursos do servidor

#### "Erro de permissão ao salvar arquivo"
- Verifique permissões do diretório temporário
- Execute com privilégios adequados
- Configure `TEMP_DIR` personalizado

### Logs e Debug

```bash
# Habilitar logs verbosos
CONVERTER_VERBOSE_LOGS=true npm run dev

# Debug do plugin Revit
# Logs aparecem no Visual Studio Output
```

## 🤝 Contribuição

1. Fork o projeto
2. Crie uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

## 📄 Licença

Este projeto está licenciado sob a Licença MIT - veja o arquivo [LICENSE](LICENSE) para detalhes.

## 🆘 Suporte

- 📧 Email: <EMAIL>
- 💬 Discord: [BIMEX Community](https://discord.gg/bimex)
- 📖 Documentação: [docs.bimex.com.br](https://docs.bimex.com.br)
- 🐛 Issues: [GitHub Issues](https://github.com/seu-usuario/bimex-object-market/issues)

## 🙏 Agradecimentos

- [pyRevit](https://github.com/eirannejad/pyRevit) - Framework para automação Revit
- [shadcn/ui](https://ui.shadcn.com/) - Componentes UI
- [Cloudflare R2](https://www.cloudflare.com/products/r2/) - Armazenamento de objetos
- [Google Gemini](https://ai.google.dev/) - IA para geração de conteúdo
