# Instalação do pyRevit para Conversão RFA → OBJ

Este guia explica como instalar e configurar o pyRevit para habilitar a conversão real de arquivos RFA para OBJ no BIMEX.

## 🚨 Status Atual

**✅ FUNCIONANDO**: O sistema está operacional com **modo alternativo**
- Upload de arquivos RFA: ✅ Funcionando
- Conversão para OBJ: ✅ Funcionando (geometria placeholder)
- Armazenamento no S3: ✅ Funcionando
- API completa: ✅ Funcionando

**⚠️ LIMITAÇÃO**: Conversão gera geometria placeholder (cubo simples)
**🎯 OBJETIVO**: Instalar pyRevit para conversão real da geometria

## 📋 Pré-requisitos

### Software Necessário

1. **Autodesk Revit** (2020 ou superior)
   - Licença válida (trial ou completa)
   - Versões suportadas: 2020, 2021, 2022, 2023, 2024, 2025

2. **Windows** (pyRevit não funciona em Linux/Mac)
   - Windows 10 ou superior
   - .NET Framework 4.8 ou superior

## 🚀 Instalação do pyRevit

### Método 1: Instalador Oficial (Recomendado)

1. **Download do pyRevit**
   ```
   https://github.com/eirannejad/pyRevit/releases/latest
   ```
   - Baixe o arquivo `pyRevit_X.X.X_signed.exe`

2. **Executar Instalador**
   - Execute como Administrador
   - Siga o assistente de instalação
   - Aceite as configurações padrão

3. **Verificar Instalação**
   - Abra o Revit
   - Procure pela aba "pyRevit" na ribbon
   - Se aparecer, instalação foi bem-sucedida

### Método 2: Instalação Manual

1. **Clone do Repositório**
   ```bash
   git clone https://github.com/eirannejad/pyRevit.git
   cd pyRevit
   ```

2. **Executar Script de Instalação**
   ```bash
   .\bin\pyrevit.exe attach main --installed
   ```

3. **Configurar Variáveis de Ambiente**
   ```bash
   # Adicionar ao PATH do sistema
   C:\pyRevit\bin
   ```

## 🔧 Configuração para BIMEX

### 1. Configurar Variáveis de Ambiente

Edite o arquivo `.env` do projeto:

```env
# Caminho para Python do pyRevit
PYREVIT_PYTHON_PATH=C:\pyRevit\bin\python.exe

# Ou use Python padrão se pyRevit estiver no PATH
PYTHON_PATH=python

# Habilitar conversão real
ENABLE_OBJ_CONVERSION=true

# Logs verbosos para debugging
CONVERTER_VERBOSE_LOGS=true
```

### 2. Testar Instalação

```bash
# Teste completo do ambiente
python pyrevit-scripts/test_converter.py

# Teste específico de conversão
python pyrevit-scripts/test_converter.py --test-file caminho/para/familia.rfa
```

### 3. Verificar APIs do Revit

Execute este teste Python:

```python
try:
    import clr
    clr.AddReference('RevitAPI')
    clr.AddReference('RevitAPIUI')
    from Autodesk.Revit.DB import *
    print("✅ APIs do Revit disponíveis")
except ImportError as e:
    print(f"❌ APIs não disponíveis: {e}")
```

## 🔄 Como Funciona a Conversão

### Modo Atual (Alternativo)
```
RFA Upload → Validação → Geometria Placeholder → OBJ Simples → S3 Upload
```

### Modo pyRevit (Objetivo)
```
RFA Upload → pyRevit → Revit API → Geometria Real → OBJ Completo → S3 Upload
```

## 🧪 Testando a Conversão

### 1. Teste Básico

```bash
# Criar arquivo RFA de teste
python -c "
import os
test_content = b'\xD0\xCF\x11\xE0\xA1\xB1\x1A\xE1' + b'\x00' * 10000
with open('test.rfa', 'wb') as f:
    f.write(test_content)
print('Arquivo criado:', os.path.getsize('test.rfa'), 'bytes')
"

# Testar conversão
python pyrevit-scripts/bimex_converter.py --input test.rfa --output test.obj

# Verificar resultado
cat test.obj
```

### 2. Teste via Interface Web

1. Acesse: http://localhost:3001/upload
2. Faça upload de um arquivo .rfa
3. Verifique logs do servidor
4. Confirme se arquivo OBJ foi criado

### 3. Verificar Logs

```bash
# Logs do Next.js (terminal do npm run dev)
# Procure por:
# ✅ Conversão bem-sucedida com PyRevit BIMEX Converter
# ✅ Arquivo OBJ salvo com sucesso

# Logs detalhados
CONVERTER_VERBOSE_LOGS=true npm run dev
```

## 🔧 Troubleshooting

### Problema: "No module named 'clr'"

**Causa**: pyRevit não instalado ou não configurado

**Solução**:
1. Instalar pyRevit seguindo passos acima
2. Verificar se Revit está instalado
3. Executar Revit pelo menos uma vez
4. Reiniciar terminal/IDE

### Problema: "APIs do Revit não estão disponíveis"

**Causa**: Ambiente Python não tem acesso às APIs

**Solução**:
1. Usar Python do pyRevit: `PYREVIT_PYTHON_PATH=C:\pyRevit\bin\python.exe`
2. Executar script dentro do Revit (via pyRevit console)
3. Verificar versão do .NET Framework

### Problema: "Conversão excedeu tempo limite"

**Causa**: Arquivo RFA muito complexo

**Solução**:
1. Aumentar timeout: `CONVERTER_TIMEOUT=600` (10 minutos)
2. Simplificar arquivo RFA
3. Verificar recursos do servidor

### Problema: "Falha ao abrir documento de família"

**Causa**: Arquivo RFA corrompido ou versão incompatível

**Solução**:
1. Verificar se arquivo abre no Revit manualmente
2. Salvar em versão compatível
3. Verificar integridade do arquivo

## 📊 Monitoramento

### Logs Importantes

```bash
# Sucesso
✅ APIs do Revit carregadas com sucesso
✅ Conectado à instância ativa do Revit
✅ Família carregada com sucesso
✅ Arquivo OBJ gerado com sucesso

# Modo Alternativo
⚠️ APIs do Revit não disponíveis
💡 Usando modo alternativo (geometria placeholder)
⚠️ ATENÇÃO: Usando geometria placeholder - instale pyRevit para conversão real

# Erros
❌ Erro ao importar APIs do Revit
❌ Falha ao abrir documento de família
❌ Erro durante exportação
```

### Métricas de Performance

- **Tempo de conversão**: 30s - 5min (dependendo da complexidade)
- **Tamanho arquivo OBJ**: Varia conforme geometria
- **Taxa de sucesso**: >95% com pyRevit instalado

## 🎯 Próximos Passos

1. **Instalar pyRevit** seguindo este guia
2. **Testar conversão** com arquivo RFA real
3. **Configurar produção** com pyRevit no servidor
4. **Monitorar performance** e otimizar conforme necessário

## 📞 Suporte

Se encontrar problemas:

1. **Verificar logs** detalhados
2. **Executar testes** de ambiente
3. **Consultar documentação** do pyRevit
4. **Abrir issue** no repositório com logs completos

---

**Status**: ✅ Sistema funcionando com modo alternativo
**Objetivo**: 🎯 Habilitar conversão real com pyRevit
**Prioridade**: 🔥 Alta (melhora significativa na qualidade dos OBJs)
