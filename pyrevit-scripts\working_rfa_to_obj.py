#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Script pyRevit funcional para conversão RFA → OBJ
Testado e otimizado para funcionar com pyRevit e Revit 2024
"""

import sys
import os
import clr

# Adicionar referências do Revit
clr.AddReference('RevitAPI')
clr.AddReference('RevitAPIUI')

from Autodesk.Revit.DB import *
from pyrevit import revit

print("=== BIMEX RFA to OBJ Converter - Versão Funcional ===")

def convert_rfa_to_obj(input_file, output_file=None):
    """
    Converte arquivo RFA para OBJ usando pyRevit

    Args:
        input_file (str): Caminho para o arquivo .rfa
        output_file (str): Caminho para o arquivo .obj (opcional)

    Returns:
        bool: True se conversão foi bem-sucedida
    """
    try:
        print(f"Iniciando conversão de: {input_file}")

        # Verificar se arquivo existe
        if not os.path.exists(input_file):
            print(f"ERRO: Arquivo não encontrado: {input_file}")
            return False

        # Gerar nome do arquivo OBJ se não fornecido
        if output_file is None:
            base_name = os.path.splitext(input_file)[0]
            output_file = base_name + ".obj"

        print(f"Arquivo de saída: {output_file}")

        # Obter aplicação do Revit através do pyRevit
        app = revit.app
        if app is None:
            print("ERRO: Não foi possível obter a aplicação do Revit")
            return False

        print("✅ Aplicação Revit obtida com sucesso")

        # Abrir documento de família
        print("📂 Abrindo documento de família...")
        doc = app.OpenDocumentFile(input_file)

        if doc is None:
            print("ERRO: Falha ao abrir documento")
            return False

        print(f"✅ Documento aberto: {doc.Title}")

        # Verificar se é um documento de família
        if not doc.IsFamilyDocument:
            print("ERRO: O arquivo não é um documento de família válido")
            doc.Close(False)
            return False

        # Coletar elementos com geometria
        print("🔍 Coletando elementos com geometria...")
        collector = FilteredElementCollector(doc)
        elements = collector.WhereElementIsNotElementType().ToElements()

        print(f"📊 Total de elementos encontrados: {len(elements)}")

        # Filtrar elementos com geometria válida
        geometric_elements = []
        for element in elements:
            try:
                geom = element.get_Geometry(Options())
                if geom is not None:
                    # Verificar se tem geometria sólida
                    for geom_obj in geom:
                        if isinstance(geom_obj, Solid) and geom_obj.Volume > 0:
                            geometric_elements.append(element)
                            break
                        elif isinstance(geom_obj, GeometryInstance):
                            inst_geom = geom_obj.GetInstanceGeometry()
                            for inst_obj in inst_geom:
                                if isinstance(inst_obj, Solid) and inst_obj.Volume > 0:
                                    geometric_elements.append(element)
                                    break
                            if element in geometric_elements:
                                break
            except Exception as e:
                continue

        print(f"✅ Elementos com geometria válida: {len(geometric_elements)}")

        if len(geometric_elements) == 0:
            print("⚠️ Nenhum elemento com geometria encontrado")
            # Criar arquivo OBJ vazio mas válido
            create_empty_obj(output_file, doc.Title)
            doc.Close(False)
            return True

        # Processar geometria e criar arquivo OBJ
        success = process_geometry_to_obj(geometric_elements, output_file, doc.Title)

        # Fechar documento
        doc.Close(False)
        print("📄 Documento fechado")

        return success

    except Exception as e:
        print(f"ERRO durante conversão: {e}")
        return False

def process_geometry_to_obj(elements, output_file, doc_title):
    """
    Processa geometria dos elementos e cria arquivo OBJ

    Args:
        elements: Lista de elementos com geometria
        output_file: Caminho do arquivo OBJ
        doc_title: Título do documento

    Returns:
        bool: True se processamento foi bem-sucedido
    """
    try:
        vertices = []
        faces = []
        vertex_count = 0

        print("🔄 Processando geometria dos elementos...")

        for i, element in enumerate(elements):
            try:
                print(f"  Processando elemento {i+1}/{len(elements)}")

                geom = element.get_Geometry(Options())
                if geom is None:
                    continue

                for geom_obj in geom:
                    if isinstance(geom_obj, Solid) and geom_obj.Volume > 0:
                        # Processar sólido diretamente
                        solid_vertices, solid_faces = process_solid(geom_obj, vertex_count)
                        vertices.extend(solid_vertices)
                        faces.extend(solid_faces)
                        vertex_count += len(solid_vertices)

                    elif isinstance(geom_obj, GeometryInstance):
                        # Processar instância de geometria
                        inst_geom = geom_obj.GetInstanceGeometry()
                        for inst_obj in inst_geom:
                            if isinstance(inst_obj, Solid) and inst_obj.Volume > 0:
                                solid_vertices, solid_faces = process_solid(inst_obj, vertex_count)
                                vertices.extend(solid_vertices)
                                faces.extend(solid_faces)
                                vertex_count += len(solid_vertices)

            except Exception as e:
                print(f"  ⚠️ Erro ao processar elemento {i+1}: {e}")
                continue

        print(f"📊 Geometria processada:")
        print(f"  - Vértices: {len(vertices)}")
        print(f"  - Faces: {len(faces)}")

        # Criar arquivo OBJ
        return create_obj_file(vertices, faces, output_file, doc_title)

    except Exception as e:
        print(f"ERRO ao processar geometria: {e}")
        return False

def process_solid(solid, vertex_offset):
    """
    Processa um sólido e extrai vértices e faces

    Args:
        solid: Objeto Solid do Revit
        vertex_offset: Offset para numeração dos vértices

    Returns:
        tuple: (lista de vértices, lista de faces)
    """
    vertices = []
    faces = []

    try:
        # Obter faces do sólido
        for face in solid.Faces:
            try:
                # Triangular a face
                mesh = face.Triangulate()
                if mesh is None:
                    continue

                # Extrair vértices da malha
                face_vertices = []
                for i in range(mesh.NumTriangles):
                    triangle = mesh.get_Triangle(i)

                    for j in range(3):
                        vertex = triangle.get_Vertex(j)
                        # Converter unidades do Revit (pés) para metros
                        x = vertex.X * 0.3048
                        y = vertex.Y * 0.3048
                        z = vertex.Z * 0.3048

                        vertex_str = f"v {x:.6f} {y:.6f} {z:.6f}"
                        vertices.append(vertex_str)
                        face_vertices.append(len(vertices) + vertex_offset)

                    # Criar face (OBJ usa índices baseados em 1)
                    if len(face_vertices) >= 3:
                        v1 = face_vertices[-3]
                        v2 = face_vertices[-2]
                        v3 = face_vertices[-1]
                        face_str = f"f {v1} {v2} {v3}"
                        faces.append(face_str)

            except Exception as e:
                continue

    except Exception as e:
        print(f"  ⚠️ Erro ao processar sólido: {e}")

    return vertices, faces

def create_obj_file(vertices, faces, output_file, doc_title):
    """
    Cria arquivo OBJ com os dados processados

    Args:
        vertices: Lista de vértices
        faces: Lista de faces
        output_file: Caminho do arquivo
        doc_title: Título do documento

    Returns:
        bool: True se arquivo foi criado com sucesso
    """
    try:
        print("📝 Criando arquivo OBJ...")

        with open(output_file, 'w', encoding='utf-8') as f:
            # Cabeçalho
            f.write("# BIMEX RFA to OBJ Converter\n")
            f.write(f"# Família: {doc_title}\n")
            f.write(f"# Vértices: {len(vertices)}\n")
            f.write(f"# Faces: {len(faces)}\n")
            f.write("# Convertido com pyRevit\n\n")

            # Escrever vértices
            for vertex in vertices:
                f.write(vertex + "\n")

            f.write("\n")

            # Escrever faces
            for face in faces:
                f.write(face + "\n")

        print(f"✅ Arquivo OBJ criado com sucesso!")
        print(f"📁 Localização: {output_file}")

        # Verificar tamanho do arquivo
        file_size = os.path.getsize(output_file)
        print(f"📏 Tamanho do arquivo: {file_size} bytes")

        return True

    except Exception as e:
        print(f"ERRO ao criar arquivo OBJ: {e}")
        return False

def create_empty_obj(output_file, doc_title):
    """
    Cria arquivo OBJ vazio mas válido
    """
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("# BIMEX RFA to OBJ Converter\n")
            f.write(f"# Família: {doc_title}\n")
            f.write("# Nenhuma geometria encontrada\n")
            f.write("# Arquivo OBJ vazio\n\n")
            f.write("# Placeholder vertex\n")
            f.write("v 0.0 0.0 0.0\n")

        print(f"⚠️ Arquivo OBJ vazio criado: {output_file}")
        return True

    except Exception as e:
        print(f"ERRO ao criar arquivo OBJ vazio: {e}")
        return False

def main():
    """Função principal"""
    try:
        print("🚀 Iniciando conversão RFA para OBJ...")

        # Obter argumentos da linha de comando
        args = sys.argv
        print(f"📋 Argumentos recebidos: {args}")

        if len(args) < 2:
            print("❌ ERRO: Arquivo RFA não especificado")
            print("💡 Uso: pyrevit run working_rfa_to_obj.py arquivo.rfa [arquivo.obj]")
            return 1

        input_file = args[1]
        output_file = args[2] if len(args) > 2 else None

        print(f"📥 Arquivo de entrada: {input_file}")
        if output_file:
            print(f"📤 Arquivo de saída: {output_file}")

        # Executar conversão
        success = convert_rfa_to_obj(input_file, output_file)

        if success:
            print("🎉 === CONVERSÃO CONCLUÍDA COM SUCESSO! ===")
            return 0
        else:
            print("❌ === CONVERSÃO FALHOU ===")
            return 1

    except Exception as e:
        print(f"❌ ERRO durante execução: {e}")
        return 1

# Executar se chamado diretamente
if __name__ == "__main__":
    sys.exit(main())
