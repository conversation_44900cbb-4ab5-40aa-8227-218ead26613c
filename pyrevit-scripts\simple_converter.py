#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Script pyRevit simples e funcional para conversão RFA → OBJ
Versão otimizada para funcionar com pyRevit
"""

import sys
import os
import clr

# Adicionar referências do Revit
clr.AddReference('RevitAPI')
clr.AddReference('RevitAPIUI')

from Autodesk.Revit.DB import *
from pyrevit import revit

def main():
    """Função principal"""
    print("=== BIMEX RFA to OBJ Converter ===")
    
    try:
        # Obter argumentos
        args = sys.argv
        print("Argumentos: " + str(args))
        
        if len(args) < 2:
            print("ERRO: Arquivo RFA nao especificado")
            return False
        
        input_file = args[1]
        print("Arquivo RFA: " + input_file)
        
        # Verificar se arquivo existe
        if not os.path.exists(input_file):
            print("ERRO: Arquivo nao encontrado: " + input_file)
            return False
        
        # Gerar nome do arquivo OBJ
        base_name = os.path.splitext(input_file)[0]
        output_file = base_name + ".obj"
        print("Arquivo OBJ: " + output_file)
        
        # Obter aplicação do Revit
        app = revit.app
        print("Aplicacao Revit obtida")
        
        # Abrir documento de família
        print("Abrindo documento...")
        doc = app.OpenDocumentFile(input_file)
        
        if doc is None:
            print("ERRO: Falha ao abrir documento")
            return False
        
        print("Documento aberto: " + doc.Title)
        
        # Verificar se é documento de família
        if not doc.IsFamilyDocument:
            print("ERRO: Nao e um documento de familia")
            doc.Close(False)
            return False
        
        # Coletar elementos
        print("Coletando elementos...")
        collector = FilteredElementCollector(doc)
        elements = collector.WhereElementIsNotElementType().ToElements()
        
        print("Total de elementos: " + str(len(elements)))
        
        # Processar geometria
        vertices = []
        faces = []
        vertex_count = 0
        
        print("Processando geometria...")
        
        for element in elements:
            try:
                geom = element.get_Geometry(Options())
                if geom is None:
                    continue
                
                for geom_obj in geom:
                    if isinstance(geom_obj, Solid) and geom_obj.Volume > 0:
                        print("Processando solido...")
                        
                        # Processar faces do sólido
                        for face in geom_obj.Faces:
                            try:
                                mesh = face.Triangulate()
                                if mesh is None:
                                    continue
                                
                                # Processar triângulos
                                for i in range(mesh.NumTriangles):
                                    triangle = mesh.get_Triangle(i)
                                    
                                    # Adicionar vértices do triângulo
                                    for j in range(3):
                                        vertex = triangle.get_Vertex(j)
                                        # Converter de pés para metros
                                        x = vertex.X * 0.3048
                                        y = vertex.Y * 0.3048
                                        z = vertex.Z * 0.3048
                                        
                                        vertex_str = "v " + str(x) + " " + str(y) + " " + str(z)
                                        vertices.append(vertex_str)
                                    
                                    # Criar face (OBJ usa índices baseados em 1)
                                    v1 = vertex_count + 1
                                    v2 = vertex_count + 2
                                    v3 = vertex_count + 3
                                    face_str = "f " + str(v1) + " " + str(v2) + " " + str(v3)
                                    faces.append(face_str)
                                    vertex_count += 3
                                    
                            except Exception as e:
                                continue
                                
                    elif isinstance(geom_obj, GeometryInstance):
                        print("Processando instancia...")
                        try:
                            inst_geom = geom_obj.GetInstanceGeometry()
                            for inst_obj in inst_geom:
                                if isinstance(inst_obj, Solid) and inst_obj.Volume > 0:
                                    # Processar sólido da instância
                                    for face in inst_obj.Faces:
                                        try:
                                            mesh = face.Triangulate()
                                            if mesh is None:
                                                continue
                                            
                                            for i in range(mesh.NumTriangles):
                                                triangle = mesh.get_Triangle(i)
                                                
                                                for j in range(3):
                                                    vertex = triangle.get_Vertex(j)
                                                    x = vertex.X * 0.3048
                                                    y = vertex.Y * 0.3048
                                                    z = vertex.Z * 0.3048
                                                    
                                                    vertex_str = "v " + str(x) + " " + str(y) + " " + str(z)
                                                    vertices.append(vertex_str)
                                                
                                                v1 = vertex_count + 1
                                                v2 = vertex_count + 2
                                                v3 = vertex_count + 3
                                                face_str = "f " + str(v1) + " " + str(v2) + " " + str(v3)
                                                faces.append(face_str)
                                                vertex_count += 3
                                                
                                        except Exception as e:
                                            continue
                        except Exception as e:
                            continue
                            
            except Exception as e:
                continue
        
        print("Vertices encontrados: " + str(len(vertices)))
        print("Faces encontradas: " + str(len(faces)))
        
        # Criar arquivo OBJ
        print("Criando arquivo OBJ...")
        
        try:
            with open(output_file, 'w') as f:
                f.write("# BIMEX RFA to OBJ Converter\n")
                f.write("# Familia: " + doc.Title + "\n")
                f.write("# Vertices: " + str(len(vertices)) + "\n")
                f.write("# Faces: " + str(len(faces)) + "\n\n")
                
                # Escrever vértices
                for vertex in vertices:
                    f.write(vertex + "\n")
                
                f.write("\n")
                
                # Escrever faces
                for face in faces:
                    f.write(face + "\n")
            
            print("Arquivo OBJ criado com sucesso!")
            print("Caminho: " + output_file)
            
            # Verificar tamanho
            file_size = os.path.getsize(output_file)
            print("Tamanho: " + str(file_size) + " bytes")
            
        except Exception as e:
            print("ERRO ao criar arquivo OBJ: " + str(e))
            return False
        
        # Fechar documento
        try:
            doc.Close(False)
            print("Documento fechado")
        except:
            pass
        
        return True
        
    except Exception as e:
        print("ERRO durante conversao: " + str(e))
        return False

# Executar conversão
if __name__ == "__main__":
    print("Iniciando conversao...")
    
    success = main()
    
    if success:
        print("=== CONVERSAO CONCLUIDA COM SUCESSO! ===")
        sys.exit(0)
    else:
        print("=== CONVERSAO FALHOU ===")
        sys.exit(1)
