# BIMEX PyRevit Converter

Sistema de conversão automática de arquivos RFA (Revit Family) para formato OBJ usando pyRevit, desenvolvido para o projeto BIMEX Object Market.

## Características

- ✅ **Conversão automática**: RFA → OBJ durante o upload
- ✅ **Sem dependência do ODA BIMRV**: Usa apenas pyRevit
- ✅ **Integração transparente**: Funciona automaticamente no processo de upload
- ✅ **Fallback robusto**: Upload continua mesmo se conversão falhar
- ✅ **Logging detalhado**: Rastreamento completo do processo

## Pré-requisitos

### Software Necessário

1. **Revit** (2020 ou superior)
   - Deve estar instalado no servidor/máquina onde roda a aplicação
   - Licença válida do Revit

2. **pyRevit** 
   - Instalar pyRevit: https://github.com/eirannejad/pyRevit
   - Configurar variáveis de ambiente se necessário

3. **Python** (3.7 ou superior)
   - Deve estar acessível via comando `python`
   - Ou configurar `PYTHON_PATH` ou `PYREVIT_PYTHON_PATH` nas variáveis de ambiente

### Configuração do Ambiente

#### Variáveis de Ambiente (opcionais)

```bash
# Caminho para o executável Python (se não estiver no PATH)
PYTHON_PATH=/caminho/para/python.exe

# Caminho específico para Python do pyRevit (se diferente)
PYREVIT_PYTHON_PATH=/caminho/para/pyrevit/python.exe
```

#### Estrutura de Arquivos

```
projeto/
├── pyrevit-scripts/
│   ├── export_obj.py          # Script principal de conversão
│   ├── bimex_converter.py     # Utilitários de conversão
│   └── README.md              # Esta documentação
├── src/
│   └── lib/
│       └── bimrv-converter.ts # Conversor TypeScript
└── temp/                      # Diretório temporário (criado automaticamente)
```

## Como Funciona

### Fluxo de Conversão

1. **Upload do arquivo RFA**: Usuário faz upload via interface web
2. **Detecção automática**: Sistema detecta arquivos `.rfa`
3. **Conversão pyRevit**: Script Python converte RFA → OBJ
4. **Upload do OBJ**: Arquivo OBJ é salvo no S3/Cloudflare R2
5. **Atualização do banco**: Caminho do OBJ é salvo no banco de dados

### Processo Técnico

```mermaid
graph TD
    A[Upload RFA] --> B{É arquivo .rfa?}
    B -->|Sim| C[Salvar RFA temporário]
    B -->|Não| H[Continuar upload normal]
    C --> D[Executar pyRevit converter]
    D --> E{Conversão OK?}
    E -->|Sim| F[Upload OBJ para S3]
    E -->|Não| G[Log erro, continuar]
    F --> I[Atualizar banco com obj_path]
    G --> I
    H --> I
    I --> J[Upload concluído]
```

## Scripts

### export_obj.py

Script principal que executa a conversão usando APIs do Revit.

**Uso:**
```bash
python export_obj.py --input arquivo.rfa --output arquivo.obj [--verbose]
```

**Características:**
- Carrega família RFA no Revit
- Extrai geometria 3D
- Exporta para formato OBJ
- Suporte a múltiplos métodos de exportação
- Logging detalhado

### bimex_converter.py

Utilitário de alto nível que facilita a integração.

**Uso:**
```bash
# Conversão
python bimex_converter.py --input arquivo.rfa --output arquivo.obj

# Informações do arquivo
python bimex_converter.py --input arquivo.rfa --info
```

**Características:**
- Validação de arquivos RFA
- Preparação automática de caminhos
- Relatórios de conversão
- Limpeza de arquivos temporários

## Integração com Next.js

### Conversor TypeScript

O arquivo `src/lib/bimrv-converter.ts` fornece interface TypeScript:

```typescript
import { BimRvConverter } from '@/lib/bimrv-converter';

// Converter arquivo
const result = await BimRvConverter.convertAndGetBuffer(
  fileBuffer, 
  'familia.rfa'
);

if (result.success) {
  console.log('OBJ convertido:', result.objBuffer);
} else {
  console.error('Erro:', result.error);
}
```

### API Route

A conversão é integrada automaticamente no endpoint `/api/families`:

```typescript
// Conversão automática durante upload
if (file.name.toLowerCase().endsWith('.rfa')) {
  const conversionResult = await BimRvConverter.convertAndGetBuffer(
    fileBuffer, 
    file.name
  );
  
  if (conversionResult.success) {
    // Upload OBJ para S3
    // Salvar caminho no banco
  }
}
```

## Banco de Dados

### Migração

Execute a migração para adicionar suporte a arquivos OBJ:

```sql
-- Executar migration_add_obj_path.sql
ALTER TABLE families ADD COLUMN IF NOT EXISTS obj_path VARCHAR(500);
```

### Schema Atualizado

```sql
CREATE TABLE families (
  id SERIAL PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  file_path VARCHAR(500) NOT NULL,
  image_path VARCHAR(500) NOT NULL,
  obj_path VARCHAR(500),              -- NOVO: caminho do arquivo OBJ
  description TEXT,
  -- ... outros campos
);
```

## Troubleshooting

### Problemas Comuns

#### 1. "APIs do Revit não estão disponíveis"

**Causa**: pyRevit não está instalado ou configurado corretamente.

**Solução**:
- Instalar pyRevit: https://github.com/eirannejad/pyRevit
- Verificar se Revit está instalado
- Executar script em ambiente com Revit disponível

#### 2. "Arquivo não parece ser uma família Revit válida"

**Causa**: Arquivo corrompido ou não é RFA válido.

**Solução**:
- Verificar integridade do arquivo
- Tentar abrir no Revit manualmente
- Verificar se arquivo não está corrompido

#### 3. "Conversão excedeu tempo limite"

**Causa**: Arquivo muito complexo ou Revit lento.

**Solução**:
- Aumentar timeout no código (padrão: 5 minutos)
- Otimizar arquivo RFA
- Verificar recursos do servidor

#### 4. "Erro ao ler arquivo OBJ convertido"

**Causa**: Falha na escrita do arquivo OBJ.

**Solução**:
- Verificar permissões de escrita
- Verificar espaço em disco
- Verificar logs detalhados

### Logs e Debugging

#### Habilitar logs verbosos:

```bash
python export_obj.py --input arquivo.rfa --output arquivo.obj --verbose
```

#### Verificar logs do Node.js:

```bash
# Logs aparecem no console do servidor Next.js
npm run dev
```

#### Logs importantes:

- `🔄 Iniciando conversão RFA → OBJ`
- `✅ Conversão bem-sucedida`
- `❌ Falha na conversão`
- `⚠️ Arquivo sem geometria 3D`

## Limitações

1. **Dependência do Revit**: Requer Revit instalado no servidor
2. **Performance**: Conversão pode ser lenta para arquivos complexos
3. **Licenciamento**: Requer licença válida do Revit
4. **Geometria**: Apenas elementos com geometria 3D são convertidos

## Desenvolvimento

### Estrutura do Código

```
pyrevit-scripts/
├── export_obj.py           # Conversão principal
│   ├── RevitOBJExporter   # Classe principal
│   ├── initialize_revit() # Inicialização
│   ├── load_family_file() # Carregamento RFA
│   └── export_to_obj()    # Exportação OBJ
├── bimex_converter.py      # Utilitários
│   ├── BimexConverter     # Classe utilitária
│   ├── validate_rfa_file() # Validação
│   └── convert_rfa_to_obj() # Interface principal
```

### Contribuindo

1. Fork do repositório
2. Criar branch para feature
3. Implementar mudanças
4. Testar com arquivos RFA reais
5. Submeter Pull Request

## Suporte

Para problemas ou dúvidas:

1. Verificar logs detalhados
2. Consultar seção Troubleshooting
3. Abrir issue no repositório
4. Incluir logs e detalhes do erro
