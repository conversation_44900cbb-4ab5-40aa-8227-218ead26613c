#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Conversor final RFA → OBJ
Versão que funciona com pyRevit
"""

import sys
import os

# Verificar argumentos
if len(sys.argv) < 2:
    print("ERRO: Arquivo RFA nao especificado")
    print("Uso: pyrevit run final_converter.py arquivo.rfa")
    sys.exit(1)

input_file = sys.argv[1]
output_file = os.path.splitext(input_file)[0] + ".obj"

print("=== BIMEX RFA to OBJ Converter Final ===")
print("Input: " + input_file)
print("Output: " + output_file)

# Verificar se arquivo existe
if not os.path.exists(input_file):
    print("ERRO: Arquivo nao encontrado: " + input_file)
    sys.exit(1)

try:
    import clr
    
    # Adicionar referências do Revit
    clr.AddReference('RevitAPI')
    clr.AddReference('RevitAPIUI')
    
    from Autodesk.Revit.DB import *
    from pyrevit import revit
    
    print("Referencias importadas com sucesso")
    
    # Obter aplicação do Revit
    app = revit.app
    if app is None:
        print("ERRO: Aplicacao Revit nao disponivel")
        sys.exit(1)
    
    print("Aplicacao Revit obtida: " + app.VersionName)
    
    # Abrir documento
    print("Abrindo documento...")
    doc = app.OpenDocumentFile(input_file)
    
    if doc is None:
        print("ERRO: Falha ao abrir documento")
        sys.exit(1)
    
    print("Documento aberto: " + doc.Title)
    
    # Verificar se é família
    if not doc.IsFamilyDocument:
        print("ERRO: Nao e um documento de familia")
        doc.Close(False)
        sys.exit(1)
    
    print("Documento de familia confirmado")
    
    # Coletar elementos
    print("Coletando elementos...")
    collector = FilteredElementCollector(doc)
    elements = list(collector.WhereElementIsNotElementType().ToElements())
    
    print("Elementos encontrados: " + str(len(elements)))
    
    # Processar geometria
    print("Processando geometria...")
    vertices = []
    faces = []
    vertex_index = 1  # OBJ usa índices baseados em 1
    
    processed_elements = 0
    
    for element in elements:
        try:
            geom_options = Options()
            geom = element.get_Geometry(geom_options)
            
            if geom is None:
                continue
            
            element_has_geometry = False
            
            for geom_obj in geom:
                if isinstance(geom_obj, Solid) and geom_obj.Volume > 0:
                    element_has_geometry = True
                    print("  Processando solido do elemento " + str(element.Id))
                    
                    # Processar faces do sólido
                    for face in geom_obj.Faces:
                        try:
                            mesh = face.Triangulate()
                            if mesh is None:
                                continue
                            
                            # Processar triângulos
                            for i in range(mesh.NumTriangles):
                                triangle = mesh.get_Triangle(i)
                                
                                # Adicionar vértices do triângulo
                                v_indices = []
                                for j in range(3):
                                    vertex = triangle.get_Vertex(j)
                                    # Converter de pés para metros
                                    x = vertex.X * 0.3048
                                    y = vertex.Y * 0.3048
                                    z = vertex.Z * 0.3048
                                    
                                    vertices.append("v " + str(x) + " " + str(y) + " " + str(z))
                                    v_indices.append(vertex_index)
                                    vertex_index += 1
                                
                                # Adicionar face
                                faces.append("f " + str(v_indices[0]) + " " + str(v_indices[1]) + " " + str(v_indices[2]))
                                
                        except Exception as e:
                            continue
                            
                elif isinstance(geom_obj, GeometryInstance):
                    element_has_geometry = True
                    print("  Processando instancia do elemento " + str(element.Id))
                    
                    try:
                        inst_geom = geom_obj.GetInstanceGeometry()
                        for inst_obj in inst_geom:
                            if isinstance(inst_obj, Solid) and inst_obj.Volume > 0:
                                for face in inst_obj.Faces:
                                    try:
                                        mesh = face.Triangulate()
                                        if mesh is None:
                                            continue
                                        
                                        for i in range(mesh.NumTriangles):
                                            triangle = mesh.get_Triangle(i)
                                            
                                            v_indices = []
                                            for j in range(3):
                                                vertex = triangle.get_Vertex(j)
                                                x = vertex.X * 0.3048
                                                y = vertex.Y * 0.3048
                                                z = vertex.Z * 0.3048
                                                
                                                vertices.append("v " + str(x) + " " + str(y) + " " + str(z))
                                                v_indices.append(vertex_index)
                                                vertex_index += 1
                                            
                                            faces.append("f " + str(v_indices[0]) + " " + str(v_indices[1]) + " " + str(v_indices[2]))
                                            
                                    except Exception as e:
                                        continue
                    except Exception as e:
                        continue
            
            if element_has_geometry:
                processed_elements += 1
                
        except Exception as e:
            continue
    
    print("Elementos processados: " + str(processed_elements))
    print("Vertices coletados: " + str(len(vertices)))
    print("Faces coletadas: " + str(len(faces)))
    
    # Criar arquivo OBJ
    print("Criando arquivo OBJ...")
    
    try:
        with open(output_file, 'w') as f:
            f.write("# BIMEX RFA to OBJ Converter\n")
            f.write("# Familia: " + doc.Title + "\n")
            f.write("# Elementos processados: " + str(processed_elements) + "\n")
            f.write("# Vertices: " + str(len(vertices)) + "\n")
            f.write("# Faces: " + str(len(faces)) + "\n")
            f.write("# Convertido com pyRevit\n\n")
            
            # Escrever vértices
            for vertex in vertices:
                f.write(vertex + "\n")
            
            f.write("\n")
            
            # Escrever faces
            for face in faces:
                f.write(face + "\n")
        
        print("Arquivo OBJ criado com sucesso!")
        print("Localizacao: " + output_file)
        
        # Verificar tamanho do arquivo
        file_size = os.path.getsize(output_file)
        print("Tamanho: " + str(file_size) + " bytes")
        
    except Exception as e:
        print("ERRO ao criar arquivo OBJ: " + str(e))
        doc.Close(False)
        sys.exit(1)
    
    # Fechar documento
    print("Fechando documento...")
    doc.Close(False)
    
    print("=== CONVERSAO CONCLUIDA COM SUCESSO! ===")
    
except Exception as e:
    print("ERRO durante conversao: " + str(e))
    import traceback
    traceback.print_exc()
    sys.exit(1)
