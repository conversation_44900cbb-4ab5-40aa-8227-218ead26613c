import { NextRequest, NextResponse } from 'next/server';
import { Pool } from 'pg';
import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';
import { BimRvConverter } from '@/lib/bimrv-converter';

// Configuração do banco de dados PostgreSQL
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
});

// Configuração do cliente S3 para Cloudflare R2
const s3 = new S3Client({
  region: 'auto',
  endpoint: process.env.CLOUDFLARE_R2_ENDPOINT,
  credentials: {
    accessKeyId: process.env.CLOUDFLARE_R2_ACCESS_KEY_ID!,
    secretAccessKey: process.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY!,
  },
});

const BUCKET = process.env.CLOUDFLARE_R2_BUCKET!;

export async function POST(req: NextRequest) {
  const formData = await req.formData();
  const title = formData.get('title') as string;
  const file = formData.get('file') as File;
  const image = formData.get('image') as File;
  const description = formData.get('description') as string;
  const room = formData.getAll('room'); // array de strings
  const category = formData.get('category') as string;
  const tags = formData.get('tags') ? (formData.get('tags') as string).split(',').map(t => t.trim()).filter(Boolean) : [];
  const manufacturer = formData.get('manufacturer') as string;
  const minimalRevitVersion = formData.get('minimal_revit_version') as string;
  const price = formData.get('price') ? Number(formData.get('price')) : null;
  const premium = formData.get('premium') === 'true';
  const productLink = formData.get('product_link') as string;
  const keyFeatures = formData.get('key_features') as string;
  const detailedDescription = formData.get('detailed_description') as string;
  const detailedSpecification = formData.get('detailed_specification') as string;

  if (!title || !file || !image) {
    return NextResponse.json({ error: 'Dados incompletos.' }, { status: 400 });
  }

  // Insere no banco para obter o id sequencial e já salva os campos extras
  const dbRes = await pool.query(
    `INSERT INTO families
      (title, file_path, image_path, description, room, category, tags, manufacturer, minimal_revit_version, price, premium, product_link, key_features, detailed_description, detailed_specification)
     VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10,$11,$12,$13,$14,$15) RETURNING id`,
    [
      title, '', '', description, room, category, tags, manufacturer, minimalRevitVersion, price, premium, productLink, keyFeatures, detailedDescription, detailedSpecification
    ]
  );
  const id = dbRes.rows[0].id;

  // Upload do arquivo principal
  const fileKey = `${id}/${file.name}`;
  const fileBuffer = Buffer.from(await file.arrayBuffer());
  await s3.send(
    new PutObjectCommand({
      Bucket: BUCKET,
      Key: fileKey,
      Body: fileBuffer,
      ContentType: file.type,
    })
  );

  // Upload da imagem
  const imageKey = `${id}/${image.name}`;
  const imageBuffer = Buffer.from(await image.arrayBuffer());
  await s3.send(
    new PutObjectCommand({
      Bucket: BUCKET,
      Key: imageKey,
      Body: imageBuffer,
      ContentType: image.type,
    })
  );

  // Conversão para OBJ se for arquivo RFA
  let objKey = null;
  if (file.name.toLowerCase().endsWith('.rfa')) {
    console.log('🔄 Iniciando conversão RFA → OBJ para:', file.name);

    try {
      const conversionResult = await BimRvConverter.convertAndGetBuffer(fileBuffer, file.name);

      if (conversionResult.success && conversionResult.objBuffer) {
        // Upload do arquivo OBJ para S3
        const objFileName = file.name.replace(/\.rfa$/i, '.obj');
        objKey = `${id}/${objFileName}`;

        await s3.send(
          new PutObjectCommand({
            Bucket: BUCKET,
            Key: objKey,
            Body: conversionResult.objBuffer,
            ContentType: 'application/octet-stream',
          })
        );

        console.log('✅ Arquivo OBJ salvo com sucesso:', objKey);
      } else {
        console.warn('⚠️ Falha na conversão para OBJ:', conversionResult.error);
        // Não falha o upload, apenas registra o aviso
      }
    } catch (error) {
      console.error('❌ Erro durante conversão OBJ:', error);
      // Não falha o upload, apenas registra o erro
    }
  }

  // Atualiza os paths no banco e a data de modificação
  await pool.query(
    'UPDATE families SET file_path = $1, image_path = $2, obj_path = $3, last_modified_at = CURRENT_TIMESTAMP WHERE id = $4',
    [fileKey, imageKey, objKey, id]
  );

  return NextResponse.json({ success: true, id, title, fileKey, imageKey });
}

export async function GET(req: NextRequest) {
  try {
    const result = await pool.query('SELECT id, title, file_path, image_path, obj_path, description, room, category, tags, manufacturer, minimal_revit_version, price, premium, product_link, key_features, detailed_description, detailed_specification, created_at FROM families ORDER BY created_at DESC');
    const families = result.rows.map(family => ({
      ...family,
      imageUrl: `${process.env.CLOUDFLARE_R2_ENDPOINT}/${process.env.CLOUDFLARE_R2_BUCKET}/${family.image_path}`,
      fileUrl: `${process.env.CLOUDFLARE_R2_ENDPOINT}/${process.env.CLOUDFLARE_R2_BUCKET}/${family.file_path}`,
      objUrl: family.obj_path ? `${process.env.CLOUDFLARE_R2_ENDPOINT}/${process.env.CLOUDFLARE_R2_BUCKET}/${family.obj_path}` : null
    }));
    return NextResponse.json(families);
  } catch (error) {
    console.error('Erro ao buscar famílias:', error);
    return NextResponse.json({ error: 'Erro ao buscar famílias' }, { status: 500 });
  }
}
