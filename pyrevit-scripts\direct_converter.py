#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Script pyRevit direto para conversão RFA → OBJ
Funciona diretamente no contexto do pyRevit
"""

import sys
import os
import clr

# Adicionar referências do Revit
clr.AddReference('RevitAPI')
clr.AddReference('RevitAPIUI')

from Autodesk.Revit.DB import *
from pyrevit import revit

def convert_family_to_obj():
    """Converte família aberta para OBJ"""
    
    # Obter argumentos
    args = sys.argv
    if len(args) < 2:
        print("ERRO: Arquivo RFA nao especificado")
        return False
    
    input_file = args[1]
    output_file = os.path.splitext(input_file)[0] + ".obj"
    
    print("=== BIMEX RFA to OBJ Converter ===")
    print("Arquivo RFA: " + input_file)
    print("Arquivo OBJ: " + output_file)
    
    try:
        # Obter aplicação do Revit
        app = revit.app
        if app is None:
            print("ERRO: Aplicacao Revit nao disponivel")
            return False
        
        print("Aplicacao Revit obtida")
        
        # Abrir documento
        print("Abrindo documento...")
        doc = app.OpenDocumentFile(input_file)
        
        if doc is None:
            print("ERRO: Falha ao abrir documento")
            return False
        
        print("Documento aberto: " + doc.Title)
        
        # Verificar se é família
        if not doc.IsFamilyDocument:
            print("ERRO: Nao e um documento de familia")
            doc.Close(False)
            return False
        
        # Coletar elementos
        collector = FilteredElementCollector(doc)
        elements = list(collector.WhereElementIsNotElementType().ToElements())
        
        print("Elementos encontrados: " + str(len(elements)))
        
        # Processar geometria
        vertices = []
        faces = []
        vertex_index = 1  # OBJ usa índices baseados em 1
        
        for element in elements:
            try:
                geom_options = Options()
                geom = element.get_Geometry(geom_options)
                
                if geom is None:
                    continue
                
                for geom_obj in geom:
                    if isinstance(geom_obj, Solid) and geom_obj.Volume > 0:
                        # Processar sólido
                        for face in geom_obj.Faces:
                            try:
                                mesh = face.Triangulate()
                                if mesh is None:
                                    continue
                                
                                # Processar triângulos
                                for i in range(mesh.NumTriangles):
                                    triangle = mesh.get_Triangle(i)
                                    
                                    # Adicionar vértices
                                    v_indices = []
                                    for j in range(3):
                                        vertex = triangle.get_Vertex(j)
                                        # Converter de pés para metros
                                        x = vertex.X * 0.3048
                                        y = vertex.Y * 0.3048
                                        z = vertex.Z * 0.3048
                                        
                                        vertices.append("v " + str(x) + " " + str(y) + " " + str(z))
                                        v_indices.append(vertex_index)
                                        vertex_index += 1
                                    
                                    # Adicionar face
                                    faces.append("f " + str(v_indices[0]) + " " + str(v_indices[1]) + " " + str(v_indices[2]))
                                    
                            except Exception as e:
                                continue
                                
                    elif isinstance(geom_obj, GeometryInstance):
                        # Processar instância
                        try:
                            inst_geom = geom_obj.GetInstanceGeometry()
                            for inst_obj in inst_geom:
                                if isinstance(inst_obj, Solid) and inst_obj.Volume > 0:
                                    for face in inst_obj.Faces:
                                        try:
                                            mesh = face.Triangulate()
                                            if mesh is None:
                                                continue
                                            
                                            for i in range(mesh.NumTriangles):
                                                triangle = mesh.get_Triangle(i)
                                                
                                                v_indices = []
                                                for j in range(3):
                                                    vertex = triangle.get_Vertex(j)
                                                    x = vertex.X * 0.3048
                                                    y = vertex.Y * 0.3048
                                                    z = vertex.Z * 0.3048
                                                    
                                                    vertices.append("v " + str(x) + " " + str(y) + " " + str(z))
                                                    v_indices.append(vertex_index)
                                                    vertex_index += 1
                                                
                                                faces.append("f " + str(v_indices[0]) + " " + str(v_indices[1]) + " " + str(v_indices[2]))
                                                
                                        except Exception as e:
                                            continue
                        except Exception as e:
                            continue
                            
            except Exception as e:
                continue
        
        print("Vertices: " + str(len(vertices)))
        print("Faces: " + str(len(faces)))
        
        # Criar arquivo OBJ
        try:
            with open(output_file, 'w') as f:
                f.write("# BIMEX RFA to OBJ Converter\n")
                f.write("# Familia: " + doc.Title + "\n")
                f.write("# Vertices: " + str(len(vertices)) + "\n")
                f.write("# Faces: " + str(len(faces)) + "\n")
                f.write("# Convertido com pyRevit\n\n")
                
                # Escrever vértices
                for vertex in vertices:
                    f.write(vertex + "\n")
                
                f.write("\n")
                
                # Escrever faces
                for face in faces:
                    f.write(face + "\n")
            
            print("Arquivo OBJ criado: " + output_file)
            
            # Verificar tamanho
            file_size = os.path.getsize(output_file)
            print("Tamanho: " + str(file_size) + " bytes")
            
        except Exception as e:
            print("ERRO ao criar arquivo: " + str(e))
            doc.Close(False)
            return False
        
        # Fechar documento
        doc.Close(False)
        print("Documento fechado")
        
        return True
        
    except Exception as e:
        print("ERRO: " + str(e))
        return False

# Executar
if __name__ == "__main__":
    success = convert_family_to_obj()
    
    if success:
        print("=== CONVERSAO CONCLUIDA! ===")
    else:
        print("=== CONVERSAO FALHOU ===")
